#!/bin/bash

# Lemomate 后端更新脚本
echo "开始更新 Lemomate 后端..."

# 错误处理
set -e

# 检查是否以root运行
if [ "$(id -u)" -ne 0 ]; then
    echo "错误：请以root用户运行此脚本（使用sudo）"
    exit 1
fi

# 进入项目目录
cd /path/to/lemomate_au

# 拉取最新代码（如果使用Git）
echo "拉取最新代码..."
git pull

# 构建后端
echo "构建后端应用..."
mvn clean package -DskipTests

# 备份当前JAR文件
echo "备份当前JAR文件..."
cp /home/<USER>/lemomate.jar /home/<USER>/lemomate.jar.bak.$(date +%Y%m%d%H%M%S)

# 停止服务
echo "停止Lemomate服务..."
systemctl stop lemomate

# 复制新的JAR文件
echo "部署新的JAR文件..."
cp target/lemomate-0.0.1-SNAPSHOT.jar /home/<USER>/lemomate.jar

# 设置权限
echo "设置文件权限..."
chmod 755 /home/<USER>/lemomate.jar

# 启动服务
echo "启动Lemomate服务..."
systemctl start lemomate

# 检查服务状态
echo "检查Lemomate服务状态..."
systemctl status lemomate

echo "更新完成！"