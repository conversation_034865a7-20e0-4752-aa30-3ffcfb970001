export default {
  // 通用
  common: {
    confirm: '确认',
    cancel: '取消',
    save: '保存',
    edit: '编辑',
    delete: '删除',
    search: '搜索',
    add: '添加',
    create: '创建',
    update: '更新',
    submit: '提交',
    reset: '重置',
    back: '返回',
    loading: '加载中...',
    noData: '暂无数据',
    operation: '操作',
    status: '状态',
    actions: '操作',
    success: '成功',
    error: '错误',
    warning: '警告',
    info: '信息',
    test: '测试',
    switch: '切换',
    unknown: '未知',
    notSet: '未设置'
  },

  // 导航栏
  navbar: {
    home: 'Lemomate',
    meetings: '会议列表',
    teamMembers: '团队成员',
    profile: '个人资料',
    createMeeting: '创建会议',
    teamApplications: '团队申请',
    userManagement: '用户管理',
    teamManagement: '团队管理',
    logout: '退出登录',
    login: '登录',
    register: '注册',
    language: '语言'
  },

  // 登录页面
  login: {
    title: '登录',
    username: '用户名',
    password: '密码',
    loginButton: '登录',
    registerLink: '还没有账号？立即注册',
    usernameRequired: '请输入用户名',
    passwordRequired: '请输入密码',
    loginSuccess: '登录成功',
    loginFailed: '登录失败，请检查用户名和密码'
  },

  // 注册页面
  register: {
    title: '注册',
    username: '用户名',
    realName: '真实姓名',
    email: '邮箱',
    password: '密码',
    confirmPassword: '确认密码',
    team: '选择团队',
    selectTeam: '请选择团队',
    registerButton: '注册',
    loginLink: '已有账号？立即登录',
    usernameRequired: '请输入用户名',
    usernameLength: '用户名长度必须在3-50个字符之间',
    realNameRequired: '请输入真实姓名',
    emailRequired: '请输入邮箱',
    emailFormat: '请输入正确的邮箱格式',
    passwordRequired: '请输入密码',
    passwordLength: '密码长度必须在6-100个字符之间',
    confirmPasswordRequired: '请确认密码',
    passwordMismatch: '两次输入的密码不一致',
    registerSuccess: '注册成功',
    registerFailed: '注册失败'
  },

  // 首页
  home: {
    welcome: '欢迎使用 Lemomate Meet',
    description: 'Lemomate Meet是一个简单易用的会议系统，帮助您轻松创建和管理在线会议。',
    noTeam: '您还没有加入任何团队',
    noTeamDesc: '请选择一个团队申请加入，或联系管理员为您分配团队。',
    availableTeams: '可申请的团队：',
    apply: '申请加入',
    currentTeam: '您当前所在的团队：',
    quickActions: '快速操作：',
    viewMeetings: '查看会议',
    viewTeamMembers: '查看团队成员',
    createMeeting: '创建会议',
    recentMeetings: '最近的会议：',
    noRecentMeetings: '暂无最近的会议',
    join: '加入',
    meetingTime: '会议时间'
  },

  // 会议相关
  meeting: {
    title: '会议',
    list: '会议列表',
    create: '创建会议',
    detail: '会议详情',
    joinMeeting: '加入会议',
    meetingTitle: '会议标题',
    description: '会议描述',
    startTime: '开始时间',
    endTime: '结束时间',
    creator: '创建者',
    team: '团队',
    roomName: '房间名称',
    meetingUrl: '会议链接',
    teamOnly: '仅限团队成员',
    publicMeeting: '所有人可参加',
    status: {
      scheduled: '已预定',
      ongoing: '进行中',
      ended: '已结束'
    },
    tabs: {
      upcoming: '即将开始',
      ongoing: '进行中',
      ended: '已结束'
    },
    noMeetings: '暂无会议',
    noUpcomingMeetings: '暂无即将开始的会议',
    noOngoingMeetings: '暂无进行中的会议',
    noEndedMeetings: '暂无已结束的会议',
    titleRequired: '会议标题不能为空',
    titleLength: '会议标题长度不能超过200个字符',
    descriptionLength: '会议描述长度不能超过500个字符',
    startTimeRequired: '开始时间不能为空',
    createSuccess: '会议创建成功',
    createFailed: '会议创建失败',
    accessRestriction: '参会限制',
    copyLink: '复制链接',
    linkCopied: '链接已复制到剪贴板',
    viewDetails: '查看详情',
    join: '加入',
    meetingEnded: '会议已结束',
    teamOnlyRestriction: '该会议仅限团队成员参加',
    pleaseLogin: '请先登录',
    fetchMeetingInfoFailed: '获取会议信息失败',
    getMeetingLinkFailed: '获取会议链接失败',
    joinMeetingFailed: '加入会议失败',
    meetingLinkCopied: '会议链接已复制到剪贴板',
    shareTip: '分享此链接给需要参加会议的人',
    meetingLink: '会议链接',
    meetingDescription: '会议描述',
    copy: '复制'
  },

  // 团队相关
  team: {
    title: '团队',
    members: '团队成员',
    applications: '团队申请',
    management: '团队管理',
    name: '团队名称',
    maxMembers: '最大成员数',
    memberCount: '成员数量',
    noTeam: '您不属于任何团队',
    noTeamDesc: '请先加入一个团队，才能查看团队成员。',
    noMembers: '暂无团队成员',
    noApplications: '暂无待审核申请',
    username: '用户名',
    realName: '姓名',
    email: '邮箱',
    role: '角色',
    applyTime: '申请时间',
    approve: '通过',
    reject: '拒绝',
    remove: '移除',
    createTeam: '创建团队',
    teamNameRequired: '团队名称不能为空',
    teamNameLength: '团队名称长度必须在2-100个字符之间',
    maxMembersRequired: '请设置最大成员数',
    createSuccess: '团队创建成功',
    createFailed: '团队创建失败',
    applicationApproved: '申请已通过',
    applicationRejected: '申请已拒绝',
    memberRemoved: '成员已移除',
    alreadyInTeam: '您已经属于一个团队',
    applicationPending: '您已经申请加入该团队，请等待审核',
    applicationSubmitted: '申请已提交，等待团队管理员审核',
    applicationFailed: '申请失败',
    confirmRemoveMember: '确定要移除成员 "{name}" 吗？',
    confirmRemoveTitle: '确认移除',
    removeMemberFailed: '移除成员失败'
  },

  // 用户相关
  user: {
    profile: '个人资料',
    management: '用户管理',
    username: '用户名',
    realName: '真实姓名',
    email: '邮箱',
    role: '角色',
    team: '团队',
    avatar: '头像',
    changeAvatar: '更换头像',
    updateProfile: '更新资料',
    updateSuccess: '资料更新成功',
    updateFailed: '资料更新失败',
    avatarUploadSuccess: '头像上传成功',
    avatarUploadFailed: '头像上传失败',
    avatarSizeLimit: '头像文件大小不能超过2MB',
    avatarFormatError: '头像只能是JPG/PNG格式',
    searchPlaceholder: '搜索用户名、姓名或邮箱',
    roleUpdated: '用户角色已更新',
    roleUpdateFailed: '更新用户角色失败',
    teamUpdated: '用户团队已更新',
    teamUpdateFailed: '更新用户团队失败',
    roles: {
      USER: '普通用户',
      TEAM_ADMIN: '团队管理员',
      PLATFORM_ADMIN: '平台管理员'
    },
    noTeam: '无'
  },

  // 错误信息
  error: {
    networkError: '网络错误，请检查网络连接',
    serverError: '服务器错误，请稍后重试',
    unauthorized: '未授权，请重新登录',
    forbidden: '权限不足',
    notFound: '页面不存在',
    validationError: '数据验证失败',
    unknownError: '未知错误',
    fetchMeetingsFailed: '获取会议列表失败',
    fetchTeamMembersFailed: '获取团队成员失败'
  },

  // 成功信息
  success: {
    operationSuccess: '操作成功',
    saveSuccess: '保存成功',
    deleteSuccess: '删除成功',
    updateSuccess: '更新成功',
    createSuccess: '创建成功'
  }
}
