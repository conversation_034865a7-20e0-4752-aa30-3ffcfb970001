import Vue from 'vue'
import VueI18n from 'vue-i18n'
import zhCN from './locales/zh-CN'
import enUS from './locales/en-US'
import ElementLocaleZhCN from 'element-ui/lib/locale/lang/zh-CN'
import ElementLocaleEnUS from 'element-ui/lib/locale/lang/en'

Vue.use(VueI18n)

const messages = {
  'zh-CN': {
    ...zhCN,
    el: ElementLocaleZhCN.el
  },
  'en-US': {
    ...enUS,
    el: ElementLocaleEnUS.el
  }
}

const i18n = new VueI18n({
  locale: localStorage.getItem('language') || 'zh-CN',
  fallbackLocale: 'zh-CN',
  messages
})

export default i18n
