<template>
  <div class="test-i18n">
    <Navbar />
    
    <div class="container">
      <h2>{{ $t('navbar.language') }} {{ $t('common.test') }}</h2>
      
      <el-card>
        <div slot="header">
          <span>{{ $t('common.test') }} {{ $t('navbar.language') }}</span>
        </div>
        
        <div class="test-content">
          <h3>{{ $t('navbar.home') }}</h3>
          <p>{{ $t('home.welcome') }}</p>
          <p>{{ $t('home.description') }}</p>
          
          <h3>{{ $t('login.title') }}</h3>
          <p>{{ $t('login.username') }}: {{ $t('login.usernameRequired') }}</p>
          <p>{{ $t('login.password') }}: {{ $t('login.passwordRequired') }}</p>
          
          <h3>{{ $t('meeting.title') }}</h3>
          <p>{{ $t('meeting.meetingTitle') }}</p>
          <p>{{ $t('meeting.status.scheduled') }}</p>
          <p>{{ $t('meeting.status.ongoing') }}</p>
          <p>{{ $t('meeting.status.ended') }}</p>
          
          <h3>{{ $t('team.title') }}</h3>
          <p>{{ $t('team.name') }}</p>
          <p>{{ $t('team.members') }}</p>
          
          <h3>{{ $t('user.profile') }}</h3>
          <p>{{ $t('user.username') }}</p>
          <p>{{ $t('user.email') }}</p>
          
          <div class="language-switch">
            <h3>{{ $t('navbar.language') }} {{ $t('common.switch') }}</h3>
            <el-button @click="switchLanguage('zh-CN')" :type="currentLanguage === 'zh-CN' ? 'primary' : 'default'">中文</el-button>
            <el-button @click="switchLanguage('en-US')" :type="currentLanguage === 'en-US' ? 'primary' : 'default'">English</el-button>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import Navbar from '@/components/Navbar.vue'
import { mapGetters } from 'vuex'

export default {
  name: 'TestI18n',
  components: {
    Navbar
  },
  computed: {
    ...mapGetters(['language']),
    currentLanguage() {
      return this.language
    }
  },
  methods: {
    switchLanguage(language) {
      this.$store.dispatch('changeLanguage', language)
      this.$i18n.setLocale(language)
      this.$forceUpdate()
    }
  }
}
</script>

<style scoped>
.test-content {
  line-height: 1.6;
}

.test-content h3 {
  color: #409EFF;
  margin-top: 20px;
  margin-bottom: 10px;
}

.test-content p {
  margin-bottom: 8px;
}

.language-switch {
  margin-top: 30px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.language-switch .el-button {
  margin-right: 10px;
}
</style>
