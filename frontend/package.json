{"name": "lemomate-frontend", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.21.1", "core-js": "^3.6.5", "element-ui": "^2.15.6", "moment": "^2.29.1", "vue": "^2.6.11", "vue-router": "^3.2.0", "vuex": "^3.4.0"}, "devDependencies": {"@babel/plugin-proposal-optional-chaining": "^7.21.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "sass": "^1.26.5", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.6.11"}}