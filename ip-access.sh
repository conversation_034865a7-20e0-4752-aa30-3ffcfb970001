#!/bin/bash

# 通过IP访问Lemomate应用的配置脚本
echo "配置通过IP访问Lemomate应用..."

# 错误处理
set -e

# 检查是否以root运行
if [ "$(id -u)" -ne 0 ]; then
    echo "错误：请以root用户运行此脚本（使用sudo）"
    exit 1
fi

# 配置Nginx
echo "更新Nginx配置..."
cat > /etc/nginx/sites-available/lemomate << EOF
# 默认服务器配置，响应所有请求（包括IP地址）
server {
    listen 80 default_server;
    server_name schedulemeet.lemomate.com **************;

    location / {
        root /home/<USER>/frontend;
        index index.html;
        try_files \$uri \$uri/ /index.html;
    }

    location /api/ {
        proxy_pass http://localhost:8085;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    location /api/uploads/ {
        alias /home/<USER>/uploads/;
        autoindex off;
    }
}
EOF

# 检查Nginx配置
echo "检查Nginx配置..."
nginx -t

# 重启Nginx
echo "重启Nginx..."
systemctl restart nginx

echo "配置完成！您现在可以通过以下方式访问应用："
echo "1. http://**************"
echo "2. http://**************/api (后端API)"
echo "3. http://schedulemeet.lemomate.com (如果DNS已配置)"
