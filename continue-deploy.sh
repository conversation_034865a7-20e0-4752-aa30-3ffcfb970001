#!/bin/bash

# Lemomate 部署脚本（继续部署，跳过SSL配置）
echo "继续部署Lemomate应用到VPS..."

# 错误处理
set -e

# 检查是否以root运行
if [ "$(id -u)" -ne 0 ]; then
    echo "错误：请以root用户运行此脚本（使用sudo）"
    exit 1
fi

# 复制前端文件
echo "部署前端文件..."
mkdir -p /home/<USER>/frontend
cp -r frontend/dist/* /home/<USER>/frontend/

# 创建服务文件
echo "创建系统服务..."
cat > /etc/systemd/system/lemomate.service << EOF
[Unit]
Description=Lemomate Backend Service
After=network.target mysql.service

[Service]
User=root
WorkingDirectory=/home/<USER>
ExecStart=/usr/bin/java -jar -Dspring.profiles.active=prod /home/<USER>/lemomate.jar
SuccessExitStatus=143
TimeoutStopSec=10
Restart=on-failure
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF

# 复制JAR文件
echo "复制JAR文件..."
cp target/lemomate-0.0.1-SNAPSHOT.jar /home/<USER>/lemomate.jar

# 设置权限
echo "设置文件权限..."
chmod 755 /home/<USER>/lemomate.jar

# 重新加载系统服务
echo "重新加载系统服务..."
systemctl daemon-reload

# 启用服务
echo "启用Lemomate服务..."
systemctl enable lemomate

# 启动服务
echo "启动Lemomate服务..."
systemctl start lemomate

# 检查服务状态
echo "检查Lemomate服务状态..."
systemctl status lemomate

# 检查服务是否成功启动
if systemctl is-active --quiet lemomate; then
    echo -e "\n\n恭喜！部署完成！"
    echo -e "\n应用已在 http://schedulemeet.lemomate.com 上线"
    echo "请确保您的域名 schedulemeet.lemomate.com 已经正确解析到服务器IP地址"

    echo -e "\n重要信息："
    echo "1. 数据库用户名： lemomate"
    echo "2. 数据库密码： your_secure_password"
    echo "3. 应用访问地址： http://schedulemeet.lemomate.com"
    echo "4. 后端服务运行在端口 8085"

    echo -e "\n维护命令："
    echo "- 查看日志： journalctl -u lemomate -f"
    echo "- 重启服务： systemctl restart lemomate"
    echo "- 停止服务： systemctl stop lemomate"
    echo "- 启动服务： systemctl start lemomate"
    echo "- 查看状态： systemctl status lemomate"
else
    echo -e "\n\n警告：Lemomate服务启动失败！"
    echo "请检查日志以获取更多信息： journalctl -u lemomate -f"
fi
