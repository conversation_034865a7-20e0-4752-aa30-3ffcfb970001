#!/bin/bash

# Lemomate 综合功能更新脚本
echo "开始更新 Lemomate 所有新功能..."

# 错误处理
set -e

# 检查是否以root运行
if [ "$(id -u)" -ne 0 ]; then
    echo "错误：请以root用户运行此脚本（使用sudo）"
    exit 1
fi

# 进入项目目录
# 获取脚本所在目录作为项目目录
PROJECT_DIR="$(cd "$(dirname "$0")" && pwd)"
cd $PROJECT_DIR

echo "当前目录: $(pwd)"

# 拉取最新代码（如果使用Git）
echo "拉取最新代码..."
git pull

# 构建前端
echo "构建前端..."
cd frontend
npm run build
cd ..

# 构建后端
echo "构建后端应用..."
mvn clean package -DskipTests -U

# 备份当前JAR文件
echo "备份当前JAR文件..."
cp /home/<USER>/lemomate.jar /home/<USER>/lemomate.jar.bak.$(date +%Y%m%d%H%M%S)

# 停止服务
echo "停止Lemomate服务..."
systemctl stop lemomate

# 复制新的JAR文件
echo "部署新的JAR文件..."
cp target/lemomate-0.0.1-SNAPSHOT.jar /home/<USER>/lemomate.jar

# 复制前端文件
echo "部署前端文件..."
rm -rf /var/www/lemomate/*
cp -r frontend/dist/* /var/www/lemomate/

# 设置权限
echo "设置文件权限..."
chmod 755 /home/<USER>/lemomate.jar
chown -R www-data:www-data /var/www/lemomate

# 启动服务
echo "启动Lemomate服务..."
systemctl start lemomate

# 等待服务启动
echo "等待服务启动..."
sleep 15

# 检查服务状态
echo "检查Lemomate服务状态..."
if systemctl is-active --quiet lemomate; then
    echo "✅ 更新成功！Lemomate服务正在运行"
    echo ""
    echo "本次更新包含的功能："
    echo ""
    echo "🔧 房间名修复功能："
    echo "1. 修复了中文会议标题导致的房间名不匹配问题"
    echo "2. 添加了中文转拼音功能（内置常用字符映射）"
    echo "3. 确保房间名只包含ASCII字符，兼容Jitsi Meet"
    echo "4. 保持中文标题显示，不影响用户体验"
    echo ""
    echo "👥 团队管理功能："
    echo "1. 团队管理员可以在团队成员页面删除成员"
    echo "2. 平台管理员可以在团队管理页面删除团队"
    echo "3. 创建团队时可以设置最大成员数量（默认50人，可设置1-1000人）"
    echo "4. 团队列表显示当前成员数/最大成员数"
    echo ""
    echo "🔒 安全特性："
    echo "- 只有空团队（无成员）才能被删除"
    echo "- 团队管理员不能移除自己，需要联系平台管理员"
    echo "- 平台管理员拥有所有权限"
    echo "- 所有删除操作都有确认对话框"
    echo ""
    echo "🎯 用户体验："
    echo "- 中文会议标题正常显示和使用"
    echo "- 例如：'项目讨论会议' 将生成房间名 'xiangmutaolunhuiyi_1234567890'"
    echo "- 用户仍然可以看到中文标题，但Jitsi会使用英文房间名"
    echo "- 操作按钮有加载状态，清晰的错误提示和成功消息"
else
    echo "❌ 服务启动失败，请检查日志"
    echo "查看日志命令: journalctl -u lemomate -f"
    exit 1
fi

echo ""
echo "🎉 所有功能更新完成！现在可以正常使用中文会议标题和新的团队管理功能了！"
