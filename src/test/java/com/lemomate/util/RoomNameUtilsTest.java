package com.lemomate.util;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

public class RoomNameUtilsTest {

    @Test
    public void testChineseToEnglish() {
        // 测试中文转换
        String result1 = RoomNameUtils.generateBaseRoomName("项目讨论会议");
        System.out.println("项目讨论会议 -> " + result1);
        assertTrue(result1.matches("[a-z0-9]+"));
        
        String result2 = RoomNameUtils.generateBaseRoomName("团队工作会议");
        System.out.println("团队工作会议 -> " + result2);
        assertTrue(result2.matches("[a-z0-9]+"));
        
        String result3 = RoomNameUtils.generateBaseRoomName("产品研发讨论");
        System.out.println("产品研发讨论 -> " + result3);
        assertTrue(result3.matches("[a-z0-9]+"));
    }

    @Test
    public void testMixedContent() {
        // 测试中英文混合
        String result1 = RoomNameUtils.generateBaseRoomName("Project会议2024");
        System.out.println("Project会议2024 -> " + result1);
        assertTrue(result1.matches("[a-z0-9]+"));
        
        String result2 = RoomNameUtils.generateBaseRoomName("技术分享Session");
        System.out.println("技术分享Session -> " + result2);
        assertTrue(result2.matches("[a-z0-9]+"));
    }

    @Test
    public void testEnglishOnly() {
        // 测试纯英文
        String result = RoomNameUtils.generateBaseRoomName("Project Meeting");
        System.out.println("Project Meeting -> " + result);
        assertTrue(result.matches("[a-z0-9]+"));
    }

    @Test
    public void testEmptyOrNull() {
        // 测试空值
        String result1 = RoomNameUtils.generateBaseRoomName("");
        assertEquals("meeting", result1);
        
        String result2 = RoomNameUtils.generateBaseRoomName(null);
        assertEquals("meeting", result2);
        
        String result3 = RoomNameUtils.generateBaseRoomName("   ");
        assertEquals("meeting", result3);
    }

    @Test
    public void testFullRoomName() {
        // 测试完整房间名生成
        String result = RoomNameUtils.generateRoomName("项目讨论会议");
        System.out.println("完整房间名: " + result);
        
        // 验证格式：基础名_时间戳
        assertTrue(result.matches("[a-z0-9]+_[0-9]+"));
        assertTrue(result.length() <= 50);
    }

    @Test
    public void testSpecialCharacters() {
        // 测试特殊字符
        String result = RoomNameUtils.generateBaseRoomName("会议@#$%^&*()");
        System.out.println("会议@#$%^&*() -> " + result);
        assertTrue(result.matches("[a-z0-9]+"));
    }
}
