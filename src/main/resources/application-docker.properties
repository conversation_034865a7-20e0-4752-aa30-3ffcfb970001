# æ°æ®åºéç½®
spring.datasource.url=***************************************************************************************
spring.datasource.username=root
spring.datasource.password=root
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPAéç½®
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.properties.hibernate.format_sql=true

# æå¡å¨éç½®
server.port=8085
server.servlet.context-path=/api

# JWTéç½®
jwt.secret=EldqqV5j3JQax6yaKe3XKKNVQLZRpT2g
jwt.expiration=86400000

# Jitsiéç½®
jitsi.app.id=lemomate_app
jitsi.domain=meet.lemomate.com

# æä»¶ä¸ä¼ éç½®
spring.servlet.multipart.enabled=true
spring.servlet.multipart.file-size-threshold=2KB
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=15MB

# æä»¶å­å¨è·¯å¾
file.upload-dir=/app/uploads/avatars