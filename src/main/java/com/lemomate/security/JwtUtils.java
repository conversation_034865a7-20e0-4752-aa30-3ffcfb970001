package com.lemomate.security;

import com.lemomate.model.Meeting;
import com.lemomate.model.User;
import com.lemomate.model.UserRole;
import io.jsonwebtoken.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import javax.crypto.spec.SecretKeySpec;
import java.security.Key;

@Component
public class JwtUtils {
    private static final Logger logger = LoggerFactory.getLogger(JwtUtils.class);

    @Value("${jwt.secret}")
    private String jwtSecret;

    @Value("${jwt.expiration}")
    private int jwtExpirationMs;

    @Value("${jitsi.app.id}")
    private String jitsiAppId;

    @Value("${jitsi.domain}")
    private String jitsiDomain;

    @Autowired
    private com.lemomate.config.AppConfig appConfig;

    /**
     * 创建签名密钥
     * 使用原始字符串而不是Base64编码
     * @param algorithm 签名算法
     * @return 密钥对象
     */
    private Key getSigningKey(SignatureAlgorithm algorithm) {
        byte[] keyBytes = jwtSecret.getBytes();
        return new SecretKeySpec(keyBytes, algorithm.getJcaName());
    }

    public String generateJwtToken(Authentication authentication) {
        UserDetailsImpl userPrincipal = (UserDetailsImpl) authentication.getPrincipal();

        return Jwts.builder()
                .setSubject(userPrincipal.getUsername())
                .setIssuedAt(new Date())
                .setExpiration(new Date((new Date()).getTime() + jwtExpirationMs))
                .signWith(SignatureAlgorithm.HS512, getSigningKey(SignatureAlgorithm.HS512))
                .compact();
    }

    public String generateJitsiToken(User user, Meeting meeting) {
        long expirationTime = System.currentTimeMillis() + 24 * 60 * 60 * 1000; // 24小时有效期

        // 判断用户角色
        boolean isAdmin = user.getRole() == UserRole.TEAM_ADMIN || user.getRole() == UserRole.PLATFORM_ADMIN;

        // 创建用户信息
        Map<String, Object> userInfo = new HashMap<>();

        // 处理头像URL
        String avatarUrl = "";
        if (user.getAvatarUrl() != null && !user.getAvatarUrl().isEmpty()) {
            // 如果头像URL包含 localhost，则替换为正确的域名
            if (user.getAvatarUrl().contains("localhost")) {
                // 从原始URL中提取文件名
                String fileName = user.getAvatarUrl().substring(user.getAvatarUrl().lastIndexOf("/") + 1);
                avatarUrl = appConfig.getFileUrl(fileName);
            } else {
                avatarUrl = user.getAvatarUrl();
            }
        }

        userInfo.put("avatar", avatarUrl);
        userInfo.put("name", user.getRealName());
        userInfo.put("email", user.getEmail());
        userInfo.put("affiliation", isAdmin ? "owner" : "member");
        userInfo.put("moderator", isAdmin);

        // 创建上下文
        Map<String, Object> context = new HashMap<>();
        context.put("user", userInfo);

        // 创建claims
        Map<String, Object> claims = new HashMap<>();
        claims.put("context", context);
        claims.put("aud", "jitsi");
        claims.put("iss", jitsiAppId);
        claims.put("sub", jitsiDomain);
        claims.put("room", meeting.getRoomName());
        claims.put("exp", expirationTime / 1000); // 转换为秒

        // 创建header
        Map<String, Object> header = new HashMap<>();
        header.put("typ", "JWT");

        return Jwts.builder()
                .setHeader(header)
                .setClaims(claims)
                .signWith(SignatureAlgorithm.HS256, getSigningKey(SignatureAlgorithm.HS256))
                .compact();
    }

    public String getUserNameFromJwtToken(String token) {
        return Jwts.parser().setSigningKey(getSigningKey(SignatureAlgorithm.HS512)).parseClaimsJws(token).getBody().getSubject();
    }

    public boolean validateJwtToken(String authToken) {
        try {
            Jwts.parser().setSigningKey(getSigningKey(SignatureAlgorithm.HS512)).parseClaimsJws(authToken);
            return true;
        } catch (SignatureException e) {
            logger.error("Invalid JWT signature: {}", e.getMessage());
        } catch (MalformedJwtException e) {
            logger.error("Invalid JWT token: {}", e.getMessage());
        } catch (ExpiredJwtException e) {
            logger.error("JWT token is expired: {}", e.getMessage());
        } catch (UnsupportedJwtException e) {
            logger.error("JWT token is unsupported: {}", e.getMessage());
        } catch (IllegalArgumentException e) {
            logger.error("JWT claims string is empty: {}", e.getMessage());
        }

        return false;
    }
}
