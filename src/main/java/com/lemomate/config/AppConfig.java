package com.lemomate.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

@Component
@Configuration
public class AppConfig {
    
    @Value("${app.domain:localhost:8085}")
    private String appDomain;
    
    public String getAppDomain() {
        return appDomain;
    }
    
    /**
     * 生成文件的完整URL
     * @param filePath 文件路径
     * @return 完整的URL
     */
    public String getFileUrl(String filePath) {
        return "https://" + appDomain + "/api/files/" + filePath;
    }
}
