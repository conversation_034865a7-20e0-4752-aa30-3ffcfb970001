package com.lemomate.util;

import java.util.HashMap;
import java.util.Map;

/**
 * 房间名工具类
 * 用于生成符合Jitsi Meet要求的房间名，同时保持中文显示
 */
public class RoomNameUtils {
    
    // 常用中文字符到拼音的映射表（简化版）
    private static final Map<String, String> CHINESE_TO_PINYIN = new HashMap<String, String>() {{
        // 常用字符
        put("会", "hui"); put("议", "yi"); put("讨", "tao"); put("论", "lun");
        put("项", "xiang"); put("目", "mu"); put("团", "tuan"); put("队", "dui");
        put("工", "gong"); put("作", "zuo"); put("学", "xue"); put("习", "xi");
        put("培", "pei"); put("训", "xun"); put("分", "fen"); put("享", "xiang");
        put("日", "ri"); put("周", "zhou"); put("月", "yue"); put("年", "nian");
        put("上", "shang"); put("下", "xia"); put("中", "zhong"); put("内", "nei");
        put("部", "bu"); put("门", "men"); put("组", "zu"); put("班", "ban");
        put("新", "xin"); put("老", "lao"); put("大", "da"); put("小", "xiao");
        put("一", "yi"); put("二", "er"); put("三", "san"); put("四", "si");
        put("五", "wu"); put("六", "liu"); put("七", "qi"); put("八", "ba");
        put("九", "jiu"); put("十", "shi");
        put("产", "chan"); put("品", "pin"); put("研", "yan"); put("发", "fa");
        put("技", "ji"); put("术", "shu"); put("计", "ji"); put("划", "hua");
        put("总", "zong"); put("结", "jie"); put("报", "bao"); put("告", "gao");
        put("客", "ke"); put("户", "hu"); put("服", "fu"); put("务", "wu");
        put("销", "xiao"); put("售", "shou"); put("市", "shi"); put("场", "chang");
        put("运", "yun"); put("营", "ying"); put("管", "guan"); put("理", "li");
        put("人", "ren"); put("事", "shi"); put("财", "cai"); put("务", "wu");
        put("早", "zao"); put("晚", "wan"); put("午", "wu"); put("间", "jian");
        put("站", "zhan"); put("立", "li"); put("例", "li"); put("常", "chang");
        put("紧", "jin"); put("急", "ji"); put("重", "zhong"); put("要", "yao");
        put("临", "lin"); put("时", "shi"); put("快", "kuai"); put("速", "su");
        // 可以根据需要添加更多常用字符
    }};
    
    /**
     * 生成房间名
     * 将中文转换为拼音，移除特殊字符，确保只包含ASCII字符
     *
     * @param title 会议标题
     * @return 处理后的房间名基础部分
     */
    public static String generateBaseRoomName(String title) {
        if (title == null || title.trim().isEmpty()) {
            return "meeting";
        }

        // 将中文转换为拼音
        String pinyin = convertToPinyin(title);

        // 只保留字母和数字
        String baseRoomName = pinyin.replaceAll("[^a-zA-Z0-9]", "");

        // 如果处理后的房间名为空或太短，使用默认前缀
        if (baseRoomName.isEmpty() || baseRoomName.length() < 3) {
            baseRoomName = "meeting";
        }

        // 转换为小写
        return baseRoomName.toLowerCase();
    }
    
    /**
     * 生成完整的房间名（包含时间戳）
     * 
     * @param title 会议标题
     * @return 完整的房间名
     */
    public static String generateRoomName(String title) {
        String baseRoomName = generateBaseRoomName(title);
        
        // 添加时间戳确保唯一性
        String roomName = baseRoomName + "_" + System.currentTimeMillis();
        
        // 如果房间名过长，截取一部分
        if (roomName.length() > 50) {
            roomName = roomName.substring(0, 45) + "_" + System.currentTimeMillis() % 10000;
        }
        
        return roomName;
    }
    
    /**
     * 将中文转换为拼音（简化版）
     * 
     * @param chinese 中文字符串
     * @return 拼音字符串
     */
    private static String convertToPinyin(String chinese) {
        if (chinese == null || chinese.trim().isEmpty()) {
            return "";
        }
        
        StringBuilder pinyin = new StringBuilder();
        char[] chars = chinese.toCharArray();
        
        for (char c : chars) {
            String charStr = String.valueOf(c);
            
            if (CHINESE_TO_PINYIN.containsKey(charStr)) {
                // 如果是已知的中文字符，转换为拼音
                pinyin.append(CHINESE_TO_PINYIN.get(charStr));
            } else if (Character.isLetterOrDigit(c)) {
                // 如果是英文字母或数字，直接保留
                pinyin.append(c);
            } else if (Character.toString(c).matches("[\u4e00-\u9fa5]")) {
                // 如果是未知的中文字符，使用通用替换
                pinyin.append("cn");
            }
            // 其他字符（特殊符号等）忽略
        }
        
        return pinyin.toString();
    }
}
