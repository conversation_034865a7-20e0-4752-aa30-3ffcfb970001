package com.lemomate.util;

/**
 * 房间名工具类（简化版）
 * 用于生成符合Jitsi Meet要求的房间名，不依赖外部库
 */
public class RoomNameUtilsSimple {
    
    /**
     * 生成房间名
     * 移除中文和特殊字符，确保只包含ASCII字符
     * 
     * @param title 会议标题
     * @return 处理后的房间名基础部分
     */
    public static String generateBaseRoomName(String title) {
        if (title == null || title.trim().isEmpty()) {
            return "meeting";
        }
        
        // 只保留英文字母和数字
        String baseRoomName = title.replaceAll("[^a-zA-Z0-9]", "");
        
        // 如果处理后的房间名为空或太短，使用默认前缀
        if (baseRoomName.isEmpty() || baseRoomName.length() < 3) {
            baseRoomName = "meeting";
        }
        
        // 转换为小写
        return baseRoomName.toLowerCase();
    }
    
    /**
     * 生成完整的房间名（包含时间戳）
     * 
     * @param title 会议标题
     * @return 完整的房间名
     */
    public static String generateRoomName(String title) {
        String baseRoomName = generateBaseRoomName(title);
        
        // 添加时间戳确保唯一性
        String roomName = baseRoomName + "_" + System.currentTimeMillis();
        
        // 如果房间名过长，截取一部分
        if (roomName.length() > 50) {
            roomName = roomName.substring(0, 45) + "_" + System.currentTimeMillis() % 10000;
        }
        
        return roomName;
    }
}
