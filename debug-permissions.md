# 权限调试指南

## 问题分析

403错误通常是由于以下原因：

1. **Spring Security权限配置问题**
2. **JWT Token中的权限信息不正确**
3. **用户角色与权限不匹配**

## 调试步骤

### 1. 检查用户当前角色

在浏览器控制台中运行：
```javascript
// 检查当前用户信息
console.log('Current user:', JSON.parse(localStorage.getItem('user')));
console.log('JWT Token:', localStorage.getItem('token'));
```

### 2. 检查JWT Token内容

可以在 https://jwt.io 网站上解码JWT token，查看其中的权限信息。

### 3. 检查后端日志

查看后端日志以了解权限验证失败的具体原因：
```bash
sudo journalctl -u lemomate -f
```

### 4. 临时解决方案

如果需要临时绕过权限检查进行测试，可以：

1. 将用户角色设置为 PLATFORM_ADMIN
2. 或者临时注释掉 @PreAuthorize 注解

## 权限配置说明

- `hasRole('ROLE_NAME')` - Spring Security会自动添加 'ROLE_' 前缀
- `hasAuthority('AUTHORITY_NAME')` - 直接匹配权限名称

我们的系统使用 `hasAuthority` 因为我们的权限名称就是：
- USER
- TEAM_ADMIN  
- PLATFORM_ADMIN

## 修复后的配置

所有权限检查现在都使用 `hasAuthority` 而不是 `hasRole`：

```java
@PreAuthorize("hasAuthority('TEAM_ADMIN') or hasAuthority('PLATFORM_ADMIN')")
@PreAuthorize("hasAuthority('PLATFORM_ADMIN')")
```
