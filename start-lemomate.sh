#!/bin/bash

# Lemomate 启动脚本
echo "正在启动 Lemomate 应用..."

# 检查Java是否安装
if ! command -v java &> /dev/null; then
    echo "错误：Java未安装，请先安装Java 11或更高版本"
    exit 1
fi

# 检查MySQL是否运行
if ! systemctl is-active --quiet mysql; then
    echo "正在启动MySQL服务..."
    systemctl start mysql
fi

# 检查Nginx是否运行
if ! systemctl is-active --quiet nginx; then
    echo "正在启动Nginx服务..."
    systemctl start nginx
fi

# 检查应用目录
if [ ! -d "/home/<USER>" ]; then
    echo "创建应用目录..."
    mkdir -p /home/<USER>/uploads/avatars
    mkdir -p /home/<USER>/logs
fi

# 检查JAR文件
if [ ! -f "/home/<USER>/lemomate.jar" ]; then
    echo "错误：找不到应用JAR文件，请确保已构建并复制到/home/<USER>/lemomate.jar"
    exit 1
fi

# 启动应用
echo "启动Lemomate后端服务..."
if systemctl is-active --quiet lemomate; then
    echo "Lemomate服务已在运行，正在重启..."
    systemctl restart lemomate
else
    echo "启动Lemomate服务..."
    systemctl start lemomate
fi

# 检查服务状态
sleep 5
if systemctl is-active --quiet lemomate; then
    echo "Lemomate服务已成功启动！"
    echo "您可以通过 https://schedulemeet.lemomate.com 访问应用"
else
    echo "错误：Lemomate服务启动失败，请检查日志："
    echo "journalctl -u lemomate -f"
fi
