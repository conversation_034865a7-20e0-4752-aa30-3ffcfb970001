# Lemomate 非Docker部署指南

本文档提供了在不使用Docker的情况下，将Lemomate应用部署到VPS服务器的详细步骤。

## 前提条件

- 一台运行Linux的VPS服务器（推荐Ubuntu 20.04或更高版本）
- 域名已配置并指向服务器IP（例如：schedulemeet.lemomate.com）
- 服务器上已安装以下软件：
  - Java 11 或更高版本
  - Maven 3.6 或更高版本
  - MySQL 8.0 或更高版本
  - Nginx
  - Node.js 14 或更高版本和npm

## 部署步骤

### 1. 准备服务器环境

```bash
# 更新系统包
sudo apt update
sudo apt upgrade -y

# 安装必要的软件
sudo apt install -y openjdk-11-jdk maven mysql-server nginx certbot python3-certbot-nginx

# 安装Node.js和npm
curl -sL https://deb.nodesource.com/setup_14.x | sudo -E bash -
sudo apt install -y nodejs
```

### 2. 配置MySQL数据库

```bash
# 启动MySQL服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 设置MySQL root密码（如果尚未设置）
sudo mysql_secure_installation

# 创建数据库和用户
sudo mysql -u root -p < db-init.sql
```

### 3. 构建后端应用

```bash
# 进入项目目录
cd /path/to/lemomate

# 使用Maven构建项目
mvn clean package -DskipTests -Pprod
```

### 4. 构建前端应用

```bash
# 进入前端项目目录
cd /path/to/lemomate/frontend

# 安装依赖
npm install

# 构建生产版本
npm run build
```

### 5. 配置Nginx

创建Nginx配置文件：

```bash
sudo nano /etc/nginx/sites-available/lemomate
```

添加以下内容：

```nginx
server {
    listen 80;
    server_name schedulemeet.lemomate.com;

    location / {
        root /home/<USER>/frontend;
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    location /api/ {
        proxy_pass http://localhost:8085;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /api/uploads/ {
        alias /home/<USER>/uploads/;
        autoindex off;
    }
}
```

启用配置并重启Nginx：

```bash
sudo ln -s /etc/nginx/sites-available/lemomate /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### 6. 配置SSL证书

```bash
sudo certbot --nginx -d schedulemeet.lemomate.com
```

### 7. 部署文件

```bash
# 创建应用目录
sudo mkdir -p /home/<USER>/uploads/avatars
sudo mkdir -p /home/<USER>/logs
sudo mkdir -p /home/<USER>/frontend

# 复制前端文件
sudo cp -r /path/to/lemomate/frontend/dist/* /home/<USER>/frontend/

# 复制后端JAR文件
sudo cp /path/to/lemomate/target/lemomate-0.0.1-SNAPSHOT.jar /home/<USER>/lemomate.jar
```

### 8. 创建系统服务

创建服务文件：

```bash
sudo nano /etc/systemd/system/lemomate.service
```

添加以下内容：

```ini
[Unit]
Description=Lemomate Backend Service
After=network.target mysql.service

[Service]
User=root
WorkingDirectory=/home/<USER>
ExecStart=/usr/bin/java -jar -Dspring.profiles.active=prod /home/<USER>/lemomate.jar
SuccessExitStatus=143
TimeoutStopSec=10
Restart=on-failure
RestartSec=5

[Install]
WantedBy=multi-user.target
```

启动服务：

```bash
sudo systemctl daemon-reload
sudo systemctl enable lemomate
sudo systemctl start lemomate
```

### 9. 检查服务状态

```bash
sudo systemctl status lemomate
sudo journalctl -u lemomate -f
```

## 自动化部署

为了简化部署过程，您可以使用提供的部署脚本：

```bash
chmod +x deploy-without-docker.sh
sudo ./deploy-without-docker.sh
```

## 常见问题

### 应用无法启动

检查日志文件：

```bash
sudo journalctl -u lemomate -f
```

### 数据库连接问题

确保MySQL服务正在运行，并且数据库凭据正确：

```bash
sudo systemctl status mysql
sudo mysql -u root -p -e "SHOW DATABASES;"
```

### Nginx配置问题

检查Nginx配置和日志：

```bash
sudo nginx -t
sudo cat /var/log/nginx/error.log
```

### SSL证书问题

重新获取SSL证书：

```bash
sudo certbot --nginx -d schedulemeet.lemomate.com
```

## 维护命令

- 重启应用：`sudo systemctl restart lemomate`
- 停止应用：`sudo systemctl stop lemomate`
- 查看日志：`sudo journalctl -u lemomate -f`
- 重启Nginx：`sudo systemctl restart nginx`

## 更新应用

当需要更新应用时，请按照以下步骤操作：

1. 拉取最新代码
2. 构建后端和前端
3. 复制新的JAR文件和前端文件
4. 重启服务

```bash
# 更新后端
sudo cp /path/to/lemomate/target/lemomate-0.0.1-SNAPSHOT.jar /home/<USER>/lemomate.jar
sudo systemctl restart lemomate

# 更新前端
sudo cp -r /path/to/lemomate/frontend/dist/* /home/<USER>/frontend/
```
