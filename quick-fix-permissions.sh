#!/bin/bash

# 快速修复权限问题
echo "快速修复权限配置..."

# 错误处理
set -e

# 检查是否以root运行
if [ "$(id -u)" -ne 0 ]; then
    echo "错误：请以root用户运行此脚本（使用sudo）"
    exit 1
fi

# 进入项目目录
PROJECT_DIR="$(cd "$(dirname "$0")" && pwd)"
cd $PROJECT_DIR

echo "当前目录: $(pwd)"

# 只构建后端
echo "构建后端应用..."
mvn clean package -DskipTests -q

# 备份当前JAR文件
echo "备份当前JAR文件..."
cp /home/<USER>/lemomate.jar /home/<USER>/lemomate.jar.bak.$(date +%Y%m%d%H%M%S)

# 停止服务
echo "停止Lemomate服务..."
systemctl stop lemomate

# 复制新的JAR文件
echo "部署新的JAR文件..."
cp target/lemomate-0.0.1-SNAPSHOT.jar /home/<USER>/lemomate.jar

# 设置权限
echo "设置文件权限..."
chmod 755 /home/<USER>/lemomate.jar

# 启动服务
echo "启动Lemomate服务..."
systemctl start lemomate

# 等待服务启动
echo "等待服务启动..."
sleep 10

# 检查服务状态
echo "检查Lemomate服务状态..."
if systemctl is-active --quiet lemomate; then
    echo "✅ 权限修复成功！Lemomate服务正在运行"
    echo ""
    echo "修复内容："
    echo "- 修复了@PreAuthorize注解中hasRole和hasAuthority的使用问题"
    echo "- 删除成员功能现在应该可以正常工作了"
    echo ""
    echo "请重新测试删除成员功能"
else
    echo "❌ 服务启动失败，请检查日志"
    echo "查看日志命令: journalctl -u lemomate -f"
    exit 1
fi

echo "修复完成！"
